

# string  - str

# numbers = i nt, float, complex

# list - [] -  [1,2,3,"bala", True, 1.2, 1+2j]
#
# tuple,
# set - {1,2,3,4,} - it is having only unique values
#
#
# dict - {} - { "name": "bala", "age": 30 }

#boolean - True, False

#None - null

my_personal_info = {
    "name": "bala",
    "age": 30,
    "address": "chennai",
    "phone": 9876543210
}

print(type(my_personal_info))



my_details = "hey this is <PERSON><PERSON> , from Python , Teacher bala "

address = " I am from Chennai"

result_string = my_details + "\n"+ address




my_details.find("Bala")

my_details.replace("Bala", "Balasundaram")

print(result_string)


# help(str)


print(my_details.find("bala"))
print(my_details.split(","))


