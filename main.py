from substraction import sub_two_numbers
from multiply import multiply_two_numbers


import requests

import mysql.connector

#get

# post methods

def add_two_numbers(a, b):
    return a + b

print("the result of substraction is:", sub_two_numbers())
print("the result of multiplication is:", multiply_two_numbers())


#

def add_two_numbers(a, b):
    return a + b

### built in keywords


# class , if , else , def , int , str, float, bool, list,
# tuple, dict, set, for, while, break, continue, return, yield,
# import, from, as, pass, global, nonlocal, lambda, try, except,
# finally, raise, assert, with, as, del, in, is, not, or, and, True, False, None


if __name__ == "__main__":

    result = add_two_numbers(5, 10)
    print(result)