def add(x, y):
    return x + y

def subtract(x, y):
    return x - y

def multiply(x, y):
    return x * y

def divide(x, y):
    if y == 0:
        return "Error: Division by zero is not allowed"
    else:
        return x / y

def calculator():
    print("Simple Calculator")
    print("----------------")

    while True:
        num1 = input("Enter the first number: ")
        try:
            num1 = float(num1)
        except ValueError:
            print("Invalid input. Please enter a number.")
            continue

        op = input("Choose an operation (+, -, *, /): ")

        if op not in ['+', '-', '*', '/']:
            print("Invalid operation. Please choose one of the following: +, -, *, /")
            continue

        num2 = input("Enter the second number: ")
        try:
            num2 = float(num2)
        except ValueError:
            print("Invalid input. Please enter a number.")
            continue

        if op == '+':
            result = add(num1, num2)
        elif op == '-':
            result = subtract(num1, num2)
        elif op == '*':
            result = multiply(num1, num2)
        elif op == '/':
            result = divide(num1, num2)

        print(f"{num1} {op} {num2} = {result}")

        again = input("Do you want to calculate again? (yes/no): ")
        if again.lower() != 'yes':
            break

calculator()